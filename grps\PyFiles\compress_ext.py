#!/usr/bin/env python3
"""
Script to pack Chrome extension into CRX file
"""

import os
import subprocess
import sys
from pathlib import Path

def pack_extension(extension_path, output_path=None, private_key_path=None):
    """
    Pack Chrome extension into CRX file
    
    Args:
        extension_path: Path to extension folder
        output_path: Output path for CRX file (optional)
        private_key_path: Path to private key file (optional)
    """
    
    extension_path = Path(extension_path).resolve()
    
    if not extension_path.exists():
        print(f"Error: Extension path does not exist: {extension_path}")
        return False
    
    if not (extension_path / "manifest.json").exists():
        print(f"Error: No manifest.json found in {extension_path}")
        return False
    
    # Set output path
    if output_path is None:
        output_path = extension_path.parent / f"{extension_path.name}.crx"
    else:
        output_path = Path(output_path)
    
    # Chrome executable paths (common locations)
    chrome_paths = [
        "/usr/bin/google-chrome",
        "/usr/bin/google-chrome-stable", 
        "/usr/bin/chromium-browser",
        "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
        "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe",
        "C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe"
    ]
    
    chrome_exe = None
    for path in chrome_paths:
        if os.path.exists(path):
            chrome_exe = path
            break
    
    if chrome_exe is None:
        print("Error: Chrome executable not found")
        print("Please install Chrome or update the chrome_paths list")
        return False
    
    # Build command
    cmd = [
        chrome_exe,
        "--pack-extension=" + str(extension_path),
        "--pack-extension-key=" + str(private_key_path) if private_key_path else ""
    ]
    
    # Remove empty arguments
    cmd = [arg for arg in cmd if arg and not arg.endswith("=")]
    
    try:
        print(f"Packing extension: {extension_path}")
        print(f"Output: {output_path}")
        print(f"Command: {' '.join(cmd)}")
        
        # Run Chrome to pack extension
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            # Chrome creates the CRX in the same directory as the extension
            generated_crx = extension_path.with_suffix('.crx')
            generated_pem = extension_path.with_suffix('.pem')
            
            if generated_crx.exists():
                # Move to desired location if different
                if generated_crx != output_path:
                    generated_crx.rename(output_path)
                
                print(f"✅ Extension packed successfully: {output_path}")
                
                if generated_pem.exists():
                    print(f"🔑 Private key saved: {generated_pem}")
                
                return True
            else:
                print("❌ CRX file was not created")
                return False
        else:
            print(f"❌ Error packing extension:")
            print(f"Return code: {result.returncode}")
            print(f"STDOUT: {result.stdout}")
            print(f"STDERR: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout: Chrome took too long to pack extension")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    if len(sys.argv) < 2:
        print("Usage: python pack_extension.py <extension_path> [output_path] [private_key_path]")
        print("Example: python pack_extension.py ./my-extension ./my-extension.crx")
        sys.exit(1)
    
    extension_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None
    private_key_path = sys.argv[3] if len(sys.argv) > 3 else None
    
    success = pack_extension(extension_path, output_path, private_key_path)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()