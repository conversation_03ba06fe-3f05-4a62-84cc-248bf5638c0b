
<!-- saved from url=(0128)https://accounts.youtube.com/accounts/CheckConnection?pmpo=https%3A%2F%2Faccounts.google.com&v=*********&timestamp=************* -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><script nonce="0utc8g5aQPJmGMeUJ6ar3g">"use strict";this.default_AccountsDomaincookiesCheckconnectionJs=this.default_AccountsDomaincookiesCheckconnectionJs||{};(function(_){var window=this;
try{
_._F_toggles_initialize=function(a){(typeof globalThis!=="undefined"?globalThis:typeof self!=="undefined"?self:this)._F_toggles_default_AccountsDomaincookiesCheckconnectionJs=a||[]};(0,_._F_toggles_initialize)([0xc000, ]);
/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var k=function(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,k);else{const c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b)},aa=function(a,b){b=Array.prototype.indexOf.call(a,b,void 0);let c;(c=b>=0)&&Array.prototype.splice.call(a,b,1);return c},ba=function(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b},ca=function(a){if(a instanceof Object&&!Object.isFrozen(a)){const b=
m(a.fileName||a.filename||a.sourceURL||p.$googDebugFname||location.href);try{a.fileName=b}catch(c){}}},da=function(){var a=p.navigator;return a&&(a=a.userAgent)?a:""},u=function(a){if(!ea||!t)return!1;for(let b=0;b<t.brands.length;b++){const {brand:c}=t.brands[b];if(c&&c.indexOf(a)!=-1)return!0}return!1},v=function(a){return da().indexOf(a)!=-1},w=function(){return ea?!!t&&t.brands.length>0:!1},fa=function(){return w()?!1:v("Opera")},ha=function(){return w()?u("Microsoft Edge"):v("Edg/")},ia=function(){return v("Firefox")||
v("FxiOS")},ja=function(){return w()?u("Chromium"):(v("Chrome")||v("CriOS"))&&!(w()?0:v("Edge"))||v("Silk")},ka=function(a){const b={};a.forEach(c=>{b[c[0]]=c[1]});return c=>b[c.find(d=>d in b)]||""},la=function(a){var b=da();if(a==="Internet Explorer"){if(w()?0:v("Trident")||v("MSIE"))if((a=/rv: *([\d\.]*)/.exec(b))&&a[1])b=a[1];else{a="";var c=/MSIE +([\d\.]+)/.exec(b);if(c&&c[1])if(b=/Trident\/(\d.\d)/.exec(b),c[1]=="7.0")if(b&&b[1])switch(b[1]){case "4.0":a="8.0";break;case "5.0":a="9.0";break;
case "6.0":a="10.0";break;case "7.0":a="11.0"}else a="7.0";else a=c[1];b=a}else b="";return b}const d=RegExp("([A-Z][\\w ]+)/([^\\s]+)\\s*(?:\\((.*?)\\))?","g");c=[];let e;for(;e=d.exec(b);)c.push([e[1],e[2],e[3]||void 0]);b=ka(c);switch(a){case "Opera":if(fa())return b(["Version","Opera"]);if(w()?u("Opera"):v("OPR"))return b(["OPR"]);break;case "Microsoft Edge":if(w()?0:v("Edge"))return b(["Edge"]);if(ha())return b(["Edg"]);break;case "Chromium":if(ja())return b(["Chrome","CriOS","HeadlessChrome"])}return a===
"Firefox"&&ia()||a==="Safari"&&v("Safari")&&!(ja()||(w()?0:v("Coast"))||fa()||(w()?0:v("Edge"))||ha()||(w()?u("Opera"):v("OPR"))||ia()||v("Silk")||v("Android"))||a==="Android Browser"&&v("Android")&&!(ja()||ia()||fa()||v("Silk"))||a==="Silk"&&v("Silk")?(b=c[2])&&b[1]||"":""},x=function(a){if(w()&&a!=="Silk"){var b=t.brands.find(({brand:c})=>c===a);if(!b||!b.version)return NaN;b=b.version.split(".")}else{b=la(a);if(b==="")return NaN;b=b.split(".")}return b.length===0?NaN:Number(b[0])},ma=function(a){p.setTimeout(()=>
{throw a;},0)},oa=function(a,b){let c,d;for(let e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(let f=0;f<na.length;f++)c=na[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}},pa=function(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(let c=0;c<a.length;c++){const d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}},qa=function(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()},ra=function(a,b){return b===void 0?a.H!==z&&
!!(2&(a.u[A]|0)):!!(2&b)&&a.H!==z},sa=function(){const a=Error("int32");ba(a,"warning");return a},ta=function(a){return a},va=function(a,b,c,d){var e=d!==void 0;d=!!d;const f=[];var g=a.length;let h,l=4294967295,q=!1;const r=!!(b&64),n=r?b&128?0:-1:void 0;if(!(b&1||(h=g&&a[g-1],h!=null&&typeof h==="object"&&h.constructor===Object?(g--,l=g):h=void 0,!r||b&128||e))){q=!0;var y;l=((y=ua)!=null?y:ta)(l-n,n,a,h,void 0)+n}b=void 0;for(e=0;e<g;e++)if(y=a[e],y!=null&&(y=c(y,d))!=null)if(r&&e>=l){const K=
e-n;let L;((L=b)!=null?L:b={})[K]=y}else f[e]=y;if(h)for(let K in h){a=h[K];if(a==null||(a=c(a,d))==null)continue;g=+K;let L;if(r&&!Number.isNaN(g)&&(L=g+n)<l)f[L]=a;else{let Ra;((Ra=b)!=null?Ra:b={})[K]=a}}b&&(q?f.push(b):f[l]=b);return f},Ba=function(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return(wa?a>=xa&&a<=ya:a[0]==="-"?pa(a,za):pa(a,Aa))?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[A]|0;return a.length===0&&
b&1?void 0:va(a,b,Ba)}if(a!=null&&a[Ca]===Da)return Ea(a);return}return a},Ea=function(a){a=a.u;return va(a,a[A]|0,Ba)},Ha=function(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error("f");b=a[A]|0;if(Fa&&1&b)throw Error("g");2048&b&&!(2&b)&&Ga();if(b&256)throw Error("h");if(b&64)return b&2048||(a[A]=b|2048),a;var c=a;b|=64;var d=c.length;if(d){var e=d-1;d=c[e];if(d!=null&&typeof d==="object"&&d.constructor===Object){const f=b&128?0:-1;e-=f;if(e>=1024)throw Error("k");for(const g in d){const h=
+g;if(h<e)c[h+f]=d[g],delete d[g];else break}b=b&-8380417|(e&1023)<<13}}}a[A]=b|2112;return a},Ga=function(){if(Fa)throw Error("j");if(Ia!=null){var a;var b=(a=Ja)!=null?a:Ja={};a=b[Ia]||0;a>=5||(b[Ia]=a+1,b=Error(),ba(b,"incident"),ma(b))}},La=function(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[A]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=Ka(a,c,!1,b&&!(c&16)):(a[A]|=34,c&4&&Object.freeze(a)));return a}if(a!=null&&a[Ca]===Da){c=a.u;const d=c[A]|0;ra(a,d)||(d&2?
b=!0:d&32&&!(d&4096)?(c[A]=d|2,a.H=z,b=!0):b=!1,b?(a=new a.constructor(c),a.T=z):a=Ka(c,d));return a}},Ka=function(a,b,c,d){d!=null||(d=!!(34&b));a=va(a,b,La,d);d=32;c&&(d|=2);b=b&8380609|d;a[A]=b;return a},B=function(a){this.src=a;this.g={};this.h=0},Ma=function(a,b,c){c.message.indexOf("Error in protected function: ")!=-1||(c.error&&c.error.stack?b(c.error):a||b(c))},Na=Object.defineProperty,Oa=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&
self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("a");},Pa=Oa(this),Qa=function(a,b){if(b)a:{var c=Pa;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&Na(c,a,{configurable:!0,writable:!0,value:b})}};Qa("Symbol.dispose",function(a){return a?a:Symbol("b")});var Sa=Sa||{},p=this||self,C=function(a,b,c){a=a.split(".");c=c||p;for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b},Ua=function(a){var b=Ta("WIZ_global_data.oxN3nb");a=b&&b[a];return a!=null?a:!1},Va=p._F_toggles_default_AccountsDomaincookiesCheckconnectionJs||[],Ta=function(a){a=a.split(".");for(var b=p,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b},D=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"},
Wa="closure_uid_"+(Math.random()*1E9>>>0),Xa=0,Ya=function(a,b,c){return a.call.apply(a.bind,arguments)},E=function(a,b,c){E=Ya;return E.apply(null,arguments)},Za=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}},$a=function(a){(0,eval)(a)},F=function(a,b){function c(){}c.prototype=b.prototype;a.v=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.U=function(d,e,f){for(var g=Array(arguments.length-
2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};F(k,Error);k.prototype.name="CustomError";var ab=function(a,b,c){Array.prototype.forEach.call(a,b,c)};var bb=function(a,b,c){c=c||p;const d=c.onerror,e=!!b;c.onerror=function(f,g,h,l,q){d&&d(f,g,h,l,q);a({message:f,fileName:g,line:h,lineNumber:h,V:l,error:q});return e}},eb=function(a){var b=Ta("window.location.href");a==null&&(a='Unknown Error of type "null/undefined"');if(typeof a==="string")return{message:a,name:"Unknown error",lineNumber:"Not available",fileName:b,stack:"Not available"};let c,d;var e=!1;try{c=a.lineNumber||a.line||"Not available"}catch(f){c="Not available",e=!0}try{d=a.fileName||
a.filename||a.sourceURL||p.$googDebugFname||b}catch(f){d="Not available",e=!0}b=cb(a);return!e&&a.lineNumber&&a.fileName&&a.stack&&a.message&&a.name?{message:a.message,name:a.name,lineNumber:a.lineNumber,fileName:a.fileName,stack:b}:(e=a.message,e==null&&(e=a.constructor&&a.constructor instanceof Function?'Unknown Error of type "'+(a.constructor.name?a.constructor.name:db(a.constructor))+'"':"Unknown Error of unknown type",typeof a.toString==="function"&&Object.prototype.toString!==a.toString&&(e+=
": "+a.toString())),{message:e,name:a.name||"UnknownError",lineNumber:c,fileName:d,stack:b||"Not available"})},cb=function(a,b){b||(b={});b[fb(a)]=!0;let c=a.stack||"";var d=a.cause;d&&!b[fb(d)]&&(c+="\nCaused by: ",d.stack&&d.stack.indexOf(d.toString())==0||(c+=typeof d==="string"?d:d.message+"\n"),c+=cb(d,b));a=a.errors;if(Array.isArray(a)){d=1;let e;for(e=0;e<a.length&&!(d>4);e++)b[fb(a[e])]||(c+="\nInner error "+d++ +": ",a[e].stack&&a[e].stack.indexOf(a[e].toString())==0||(c+=typeof a[e]==="string"?
a[e]:a[e].message+"\n"),c+=cb(a[e],b));e<a.length&&(c+="\n... "+(a.length-e)+" more inner errors")}return c},fb=function(a){let b="";typeof a.toString==="function"&&(b=""+a);return b+a.stack},gb=function(a){var b=Error();if(Error.captureStackTrace)Error.captureStackTrace(b,a||gb),b=String(b.stack);else{try{throw b;}catch(c){b=c}b=(b=b.stack)?String(b):null}b||(b=hb(a||arguments.callee.caller,[]));return b},hb=function(a,b){const c=[];if(Array.prototype.indexOf.call(b,a,void 0)>=0)c.push("[...circular reference...]");
else if(a&&b.length<50){c.push(db(a)+"(");const e=a.arguments;for(let f=0;e&&f<e.length;f++){f>0&&c.push(", ");var d=void 0;d=e[f];switch(typeof d){case "object":d=d?"object":"null";break;case "string":break;case "number":d=String(d);break;case "boolean":d=d?"true":"false";break;case "function":d=(d=db(d))?d:"[fn]";break;default:d=typeof d}d.length>40&&(d=d.slice(0,40)+"...");c.push(d)}b.push(a);c.push(")\n");try{c.push(hb(a.caller,b))}catch(f){c.push("[exception trying to get caller]\n")}}else a?
c.push("[...long stack...]"):c.push("[end]");return c.join("")},db=function(a){if(G[a])return G[a];a=String(a);if(!G[a]){const b=/function\s+([^\(]+)/m.exec(a);G[a]=b?b[1]:"[Anonymous]"}return G[a]},G={};var ib=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),jb=function(a,b){if(!b)return a;var c=a.indexOf("#");c<0&&(c=a.length);let d=a.indexOf("?"),e;d<0||d>c?(d=c,e=""):e=a.substring(d+1,c);a=[a.slice(0,d),e,a.slice(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;return a[0]+(a[1]?"?"+a[1]:"")+a[2]},kb=function(a,b,c){if(Array.isArray(b))for(let d=0;d<b.length;d++)kb(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+
encodeURIComponent(String(b))))},lb=function(a,b){const c=[];for(b=b||0;b<a.length;b+=2)kb(a[b],a[b+1],c);return c.join("&")},mb=function(a){const b=[];for(const c in a)kb(c,a[c],b);return b.join("&")},nb=function(a,b){const c=arguments.length==2?lb(arguments[1],0):lb(arguments,1);return jb(a,c)};var m;m=a=>{if(!a)return a;try{a=new URL(typeof a==="object"?a.href:a)}catch(b){return typeof a==="object"?a.href:a}if(a.protocol!=="http:"&&a.protocol!=="https:")return a.protocol.slice(0,-1);a.username="";a.password="";a.hash="";return a.href};var H=function(){this.m=this.m;this.A=this.A};H.prototype.m=!1;H.prototype.dispose=function(){this.m||(this.m=!0,this.h())};H.prototype[Symbol.dispose]=function(){this.dispose()};H.prototype.h=function(){if(this.A)for(;this.A.length;)this.A.shift()()};var ob=!!(Va[0]>>15&1),pb=!!(Va[0]>>16&1),qb=!!(Va[0]&128);var ea=ob?pb:Ua(610401301),Fa=ob?qb:Ua(748402147);var t,rb=p.navigator;t=rb?rb.userAgentData||null:null;var sb=typeof AsyncContext!=="undefined"&&typeof AsyncContext.Snapshot==="function"?a=>a&&AsyncContext.Snapshot.wrap(a):a=>a;var I=[],J=[],tb=!1,ub=function(a){I[I.length]=a;if(tb)for(let b=0;b<J.length;b++)a(E(J[b].g,J[b]))},vb=function(a){tb=!0;const b=E(a.g,a);for(let c=0;c<I.length;c++)I[c](b);J.push(a)};ub(function(){});var xb=function(a){a.g&&(ab(a.g,function(b){wb(this.i,b.e,b.h,b.g)},a),a.g=null)},zb=function(a){var b=yb;b.i=a;xb(b)},Bb=function(a){var b=yb;ca(a);if(b.j>=3)throw Error("e`null");b.j++;try{b.m||(b.i?wb(b.i,a,null,"severe"):b.g&&b.g.length<10&&b.g.push(new Ab(a)))}finally{b.j--}},yb=new class extends H{constructor(){super();this.j=0;this.i=this.g=null}init(){this.g=[]}},Ab=class{constructor(a){this.h=null;this.e=a;this.g="severe"}};var na="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");var Cb=function(){const a=window;if(!a.location)try{JSON.stringify(a)}catch(c){var b=0;for(const d in a)b++}b=a.location&&a.location.ancestorOrigins;if(b!==void 0)return b&&b.length?b[b.length-1]==a.location.origin:!0;try{return a.top.location.href!==void 0}catch(c){return!1}};var Db={};var wa=typeof p.BigInt==="function"&&typeof p.BigInt(0)==="bigint";var za=Number.MIN_SAFE_INTEGER.toString(),xa=wa?BigInt(Number.MIN_SAFE_INTEGER):void 0,Aa=Number.MAX_SAFE_INTEGER.toString(),ya=wa?BigInt(Number.MAX_SAFE_INTEGER):void 0;var Ia=qa(),Ca=qa("m_m",!0);var A=qa("jas",!0);var Da={},z={};var Ja=void 0;var Eb=Number.isFinite;var ua;var Fb=function(a,b,c){if(a.H===z){var d=a.u;d=Ka(d,d[A]|0);d[A]|=2048;a.u=d;a.H=void 0;a.T=void 0;d=!0}else d=!1;if(!d&&ra(a,a.u[A]|0))throw Error();d=a.u;a:{var e=d[A]|0;const f=b+-1,g=d.length-1;if(g>=0&&f>=g){const h=d[g];if(h!=null&&typeof h==="object"&&h.constructor===Object){h[b]=c;break a}}f<=g?d[f]=c:c!==void 0&&(e=(e!=null?e:d[A]|0)>>13&1023||536870912,b>=e?c!=null&&(d[e+-1]={[b]:c}):d[f]=c)}return a},Gb=function(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return Fb(a,b,c)};var Hb=class{constructor(a){this.u=Ha(a)}toJSON(){var a=Ea(this);return a}};Hb.prototype[Ca]=Da;Hb.prototype.toString=function(){return this.u.toString()};var Ib=class extends Hb{};var M=function(a){H.call(this);this.l=a;this.j=!0;this.i=!1};F(M,H);M.prototype.g=function(a){return Jb(this,a)};
var N=function(a,b){a=Object.prototype.hasOwnProperty.call(a,Wa)&&a[Wa]||(a[Wa]=++Xa);return(b?"__wrapper_":"__protected_")+a+"__"},Jb=function(a,b){const c=N(a,!0);b[c]||((b[c]=Kb(a,b))[N(a,!1)]=b);return b[c]},Kb=function(a,b){const c=function(){if(a.m)return b.apply(this,arguments);try{return b.apply(this,arguments)}catch(d){Lb(a,d)}};c[N(a,!1)]=b;return c},Lb=function(a,b){if(!(b&&typeof b==="object"&&typeof b.message==="string"&&b.message.indexOf("Error in protected function: ")==0||typeof b===
"string"&&b.indexOf("Error in protected function: ")==0)){a.l(b);if(!a.j)throw a.i&&(typeof b==="object"&&b&&typeof b.message==="string"?b.message="Error in protected function: "+b.message:b="Error in protected function: "+b),b;throw new Mb(b);}},Nb=function(a){var b=b||p.window||p.globalThis;"onunhandledrejection"in b&&(b.onunhandledrejection=c=>{Lb(a,c&&c.reason?c.reason:Error("m"))})},Ob=function(a){const b=p.window||p.globalThis,c=["requestAnimationFrame","mozRequestAnimationFrame","webkitAnimationFrame",
"msRequestAnimationFrame"];for(let d=0;d<c.length;d++){const e=c[d];c[d]in b&&O(a,e)}},O=function(a,b){const c=p.window||p.globalThis,d=c[b];if(!d)throw Error("n`"+b);c[b]=function(e,f){typeof e==="string"&&(e=Za($a,e));e&&(arguments[0]=e=Jb(a,e));if(d.apply)return d.apply(this,arguments);let g=e;if(arguments.length>2){const h=Array.prototype.slice.call(arguments,2);g=function(){e.apply(this,h)}}return d(g,f)};c[b][N(a,!1)]=d};
M.prototype.h=function(){const a=p.window||p.globalThis;var b=a.setTimeout;b=b[N(this,!1)]||b;a.setTimeout=b;b=a.setInterval;b=b[N(this,!1)]||b;a.setInterval=b;M.v.h.call(this)};var Mb=function(a){k.call(this,"Error in protected function: "+(a&&a.message?String(a.message):String(a)),a);(a=a&&a.stack)&&typeof a==="string"&&(this.stack=a)};F(Mb,k);var P=function(a,b){this.type=a;this.g=this.target=b;this.defaultPrevented=!1};P.prototype.h=function(){this.defaultPrevented=!0};var Pb=function(){if(!p.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{const c=()=>{};p.addEventListener("test",c,b);p.removeEventListener("test",c,b)}catch(c){}return a}();var Q=function(a,b){P.call(this,a?a.type:"");this.relatedTarget=this.g=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=0;this.key="";this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.i=null;a&&this.init(a,b)};F(Q,P);
Q.prototype.init=function(a,b){const c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.g=b;b=a.relatedTarget;b||(c=="mouseover"?b=a.fromElement:c=="mouseout"&&(b=a.toElement));this.relatedTarget=b;d?(this.clientX=d.clientX!==void 0?d.clientX:d.pageX,this.clientY=d.clientY!==void 0?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.clientX=a.clientX!==void 0?a.clientX:a.pageX,this.clientY=a.clientY!==
void 0?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.key=a.key||"";this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType=a.pointerType;this.state=a.state;this.i=a;a.defaultPrevented&&Q.v.h.call(this)};Q.prototype.h=function(){Q.v.h.call(this);const a=this.i;a.preventDefault?a.preventDefault():a.returnValue=!1};var R="closure_listenable_"+(Math.random()*1E6|0);var Qb=0;var Rb=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.J=e;this.key=++Qb;this.D=this.G=!1},S=function(a){a.D=!0;a.listener=null;a.proxy=null;a.src=null;a.J=null};B.prototype.add=function(a,b,c,d,e){const f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.h++);const g=Sb(a,b,d,e);g>-1?(b=a[g],c||(b.G=!1)):(b=new Rb(b,this.src,f,!!d,e),b.G=c,a.push(b));return b};var Tb=function(a,b){const c=b.type;c in a.g&&aa(a.g[c],b)&&(S(b),a.g[c].length==0&&(delete a.g[c],a.h--))},Sb=function(a,b,c,d){for(let e=0;e<a.length;++e){const f=a[e];if(!f.D&&f.listener==b&&f.capture==!!c&&f.J==d)return e}return-1};var Ub="closure_lm_"+(Math.random()*1E6|0),Vb={},Wb=0,Yb=function(a,b,c,d,e){if(d&&d.once)Xb(a,b,c,d,e);else if(Array.isArray(b))for(let f=0;f<b.length;f++)Yb(a,b[f],c,d,e);else c=Zb(c),a&&a[R]?a.i.add(String(b),c,!1,D(d)?!!d.capture:!!d,e):$b(a,b,c,!1,d,e)},$b=function(a,b,c,d,e,f){if(!b)throw Error("o");const g=D(e)?!!e.capture:!!e;let h=ac(a);h||(a[Ub]=h=new B(a));c=h.add(b,c,d,g,f);if(!c.proxy){d=bc();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)Pb||(e=g),e===void 0&&(e=!1),a.addEventListener(b.toString(),
d,e);else if(a.attachEvent)a.attachEvent(cc(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("p");Wb++}},bc=function(){const a=dc,b=function(c){return a.call(b.src,b.listener,c)};return b},Xb=function(a,b,c,d,e){if(Array.isArray(b))for(let f=0;f<b.length;f++)Xb(a,b[f],c,d,e);else c=Zb(c),a&&a[R]?a.i.add(String(b),c,!0,D(d)?!!d.capture:!!d,e):$b(a,b,c,!0,d,e)},ec=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)ec(a,b[f],c,d,e);else(d=D(d)?
!!d.capture:!!d,c=Zb(c),a&&a[R])?(a=a.i,f=String(b).toString(),f in a.g&&(b=a.g[f],c=Sb(b,c,d,e),c>-1&&(S(b[c]),Array.prototype.splice.call(b,c,1),b.length==0&&(delete a.g[f],a.h--)))):a&&(a=ac(a))&&(b=a.g[b.toString()],a=-1,b&&(a=Sb(b,c,d,e)),(c=a>-1?b[a]:null)&&fc(c))},fc=function(a){if(typeof a!=="number"&&a&&!a.D){var b=a.src;if(b&&b[R])Tb(b.i,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(cc(c),d):b.addListener&&b.removeListener&&
b.removeListener(d);Wb--;(c=ac(b))?(Tb(c,a),c.h==0&&(c.src=null,b[Ub]=null)):S(a)}}},cc=function(a){return a in Vb?Vb[a]:Vb[a]="on"+a},dc=function(a,b){if(a.D)a=!0;else{b=new Q(b,this);const c=a.listener,d=a.J||a.src;a.G&&fc(a);a=c.call(d,b)}return a},ac=function(a){a=a[Ub];return a instanceof B?a:null},hc="__closure_events_fn_"+(Math.random()*1E9>>>0),Zb=function(a){if(typeof a==="function")return a;a[hc]||(a[hc]=function(b){return a.handleEvent(b)});return a[hc]};ub(function(a){dc=a(dc)});var T=function(){H.call(this);this.i=new B(this);this.R=this;this.L=null};F(T,H);T.prototype[R]=!0;T.prototype.addEventListener=function(a,b,c,d){Yb(this,a,b,c,d)};T.prototype.removeEventListener=function(a,b,c,d){ec(this,a,b,c,d)};
var V=function(a,b){var c,d=a.L;if(d)for(c=[];d;d=d.L)c.push(d);a=a.R;d=b.type||b;if(typeof b==="string")b=new P(b,a);else if(b instanceof P)b.target=b.target||a;else{var e=b;b=new P(d,a);oa(b,e)}e=!0;let f,g;if(c)for(g=c.length-1;g>=0;g--)f=b.g=c[g],e=U(f,d,!0,b)&&e;f=b.g=a;e=U(f,d,!0,b)&&e;e=U(f,d,!1,b)&&e;if(c)for(g=0;g<c.length;g++)f=b.g=c[g],e=U(f,d,!1,b)&&e};
T.prototype.h=function(){T.v.h.call(this);if(this.i){var a=this.i;let b=0;for(const c in a.g){const d=a.g[c];for(let e=0;e<d.length;e++)++b,S(d[e]);delete a.g[c];a.h--}}this.L=null};var U=function(a,b,c,d){b=a.i.g[String(b)];if(!b)return!0;b=b.concat();let e=!0;for(let f=0;f<b.length;++f){const g=b[f];if(g&&!g.D&&g.capture==c){const h=g.listener,l=g.J||g.src;g.G&&Tb(a.i,g);e=h.call(l,d)!==!1&&e}}return e&&!d.defaultPrevented};var W=function(){T.call(this);this.headers=new Map;this.j=!1;this.g=null;this.F="";this.l=this.C=this.o=this.B=!1;this.M=null;this.O="";this.P=!1};F(W,T);var ic=/^https?$/i,jc=["POST","PUT"],kc=[];W.prototype.S=function(){this.dispose();aa(kc,this)};
var nc=function(a,b,c,d,e){if(a.g)throw Error("q`"+a.F+"`"+b);c=c?c.toUpperCase():"GET";a.F=b;a.B=!1;a.j=!0;a.g=new XMLHttpRequest;a.g.onreadystatechange=sb(E(a.N,a));try{a.C=!0,a.g.open(c,String(b),!0),a.C=!1}catch(g){lc(a);return}b=d||"";d=new Map(a.headers);if(e)if(Object.getPrototypeOf(e)===Object.prototype)for(var f in e)d.set(f,e[f]);else if(typeof e.keys==="function"&&typeof e.get==="function")for(const g of e.keys())d.set(g,e.get(g));else throw Error("r`"+String(e));e=Array.from(d.keys()).find(g=>
"content-type"==g.toLowerCase());f=p.FormData&&b instanceof p.FormData;!(Array.prototype.indexOf.call(jc,c,void 0)>=0)||e||f||d.set("Content-Type","application/x-www-form-urlencoded;charset=utf-8");for(const [g,h]of d)a.g.setRequestHeader(g,h);a.O&&(a.g.responseType=a.O);"withCredentials"in a.g&&a.g.withCredentials!==a.P&&(a.g.withCredentials=a.P);try{mc(a),a.o=!0,a.g.send(b),a.o=!1}catch(g){lc(a)}},lc=function(a){a.j=!1;a.g&&(a.l=!0,a.g.abort(),a.l=!1);oc(a);X(a)},oc=function(a){a.B||(a.B=!0,V(a,
"complete"),V(a,"error"))};W.prototype.abort=function(){this.g&&this.j&&(this.j=!1,this.l=!0,this.g.abort(),this.l=!1,V(this,"complete"),V(this,"abort"),X(this))};W.prototype.h=function(){this.g&&(this.j&&(this.j=!1,this.l=!0,this.g.abort(),this.l=!1),X(this,!0));W.v.h.call(this)};W.prototype.N=function(){this.m||(this.C||this.o||this.l?pc(this):this.K())};W.prototype.K=function(){pc(this)};
var pc=function(a){if(a.j&&typeof Sa!="undefined")if(a.o&&(a.g?a.g.readyState:0)==4)setTimeout(a.N.bind(a),0);else if(V(a,"readystatechange"),(a.g?a.g.readyState:0)==4){a.j=!1;try{try{var b=(a.g?a.g.readyState:0)>2?a.g.status:-1}catch(f){b=-1}a:switch(b){case 200:case 201:case 202:case 204:case 206:case 304:case 1223:var c=!0;break a;default:c=!1}var d;if(!(d=c)){var e;if(e=b===0){let f=String(a.F).match(ib)[1]||null;!f&&p.self&&p.self.location&&(f=p.self.location.protocol.slice(0,-1));e=!ic.test(f?
f.toLowerCase():"")}d=e}d?(V(a,"complete"),V(a,"success")):oc(a)}finally{X(a)}}},X=function(a,b){if(a.g){mc(a);const c=a.g;a.g=null;b||V(a,"ready");try{c.onreadystatechange=null}catch(d){}}},mc=function(a){a.M&&(clearTimeout(a.M),a.M=null)};W.prototype.isActive=function(){return!!this.g};ub(function(a){W.prototype.K=a(W.prototype.K)});var Y=function(a,b,c){T.call(this);this.l=b||null;this.j={};this.B=qc;this.C=a;c||(this.g=null,this.g=new M(E(this.o,this)),O(this.g,"setTimeout"),O(this.g,"setInterval"),Ob(this.g),vb(this.g))};F(Y,T);var rc=function(a){P.call(this,"a");this.error=a};F(rc,P);var qc=function(a,b,c,d){if(d instanceof Map){var e={};for(const [f,g]of d)e[f]=g}else e=d;d=e;e=new W;kc.push(e);e.i.add("ready",e.S,!0,void 0,void 0);nc(e,a,b,c,d)};
Y.prototype.o=function(a,b){a=a.error||a;if(b){var c={};for(var d in b)c[d]=b[d];b=c}else b={};a instanceof Error&&oa(b,a.__closure__error__context__984382||{});a=eb(a);if(this.l)try{this.l(a,b)}catch(l){}d=a.message.substring(0,1900);var e=a.fileName,f=a.lineNumber;c=a.stack;try{let l=nb(this.C,"script",e,"error",d,"line",f);a:{for(const r in this.j){var g=!1;break a}g=!0}if(!g){g=l;var h=mb(this.j);l=jb(g,h)}h={};h.trace=c;if(b)for(const r in b)h["context."+r]=b[r];let q=mb(h);this.B(l,"POST",q,
this.F)}catch(l){}try{V(this,new rc(a,b))}catch(l){}};Y.prototype.h=function(){var a=this.g;a&&typeof a.dispose=="function"&&a.dispose();Y.v.h.call(this)};var wb=function(a,b,c,d="unknown"){const e={};e.location=m(location);if(Cb())try{e["top.location"]=m(top.location)}catch(f){e["top.location"]="[external]"}else e["top.location"]="[external]";for(let f in Db)try{e[f]=Db[f].call()}catch(g){e[f]="[error] "+g.message}c&&(e.message=c);sc(a,b,e,d)},sc=function(a,b,c,d){let e;const f=gb();c["call-stack"]=f;b instanceof Error?e=b:e=b||"";c.severity||(c.severity=d);for(b=0;b<a.i.length;b++)if(a.i[b](e,c)===!1)return;tc(a,c);if(a.m>=0){var g;const h=(g=c.message)!=
null?g:"",l=uc(e,c);if(g=a.g.get(l)){g.I++;a.A&&g.I===1&&vc(a,e,"Throttling: "+h,c,1);return}const q={I:0};a.g.set(l,q);setTimeout(()=>{a.g.delete(l);a.o&&q.I>0&&vc(a,e,"Throttled: "+h,c,q.I)},a.m)}wc(a,e,c)},tc=function(a,b){b.severity==="severe"&&(a.j&&(b.severity="severe_after_initial"),a.j=!0)},uc=function(a,b){const c=[];if(a){var d=a.message;a=a.stack;d&&c.push("error|:"+Z(d));a&&c.push("trace|:"+Z(a))}d=[];for(const e in b)d.push(Z(e)+"|:"+Z(b[e]));return c.join("|;")+"|."+d.join("|;")},vc=
function(a,b,c,d,e){d.message=c;d["dropped-instances"]=String(e);wc(a,b,d)},wc=function(a,b,c){ca(b);a.l.o(b,c);if(a.h){b=b.error||b;b=eb(b);c=b.message.substring(0,1900);var d=new Ib;c=Gb(d,3,c);c=Gb(c,1,b.stack);d=m(b.fileName);c=Gb(c,6,d);b=Number(b.lineNumber);if(!Number.isNaN(b)){if(b!=null){if(typeof b!=="number")throw sa();if(!Eb(b))throw sa();b|=0}Fb(c,2,b)}a.h.W(c)}},xc=class{constructor(a){({Y:d=!0,X:c=!0,Z:b=1E4}={});var b,c,d;this.l=a;this.g=new Map;this.i=[];this.j=!1;this.h=void 0;this.A=
d;this.o=c;this.m=b}},Z=a=>String(a).replace(/\|/g,()=>"||");var yc=class{};yc.prototype.g=null;yc.prototype.h=null;var zc=new yc;(function(a,b,c,d,e,f,g,h,l,q){yb.init();c&&(a=new Y(c,void 0,!0),h&&(a.j=h),e&&(a.B=e),e=new xc(a),zc.h=e,l&&(e.h=l),zb(e));l=n=>{Bb(n)};let r=null;e=function(n){p.$googDebugFname&&n&&n.message&&!n.fileName&&(n.message+=" in "+p.$googDebugFname);r?n&&n.message&&(n.message+=" [Possibly caused by: "+r+"]"):r=String(n);Bb(n)};C("_DumpException",e);C("_B_err",e);q&&C("_DumpException",e,q);ab([p].concat(d||[]),Za(bb,Za(Ma,!!f,l),!0));d=x("Chromium")>=28||x("Firefox")>=14||x("Internet Explorer")>=11||
x("Safari")>=10;g&&d||x("Internet Explorer")<=9||(g=new M(l),g.j=!0,g.i=!0,Ob(g),O(g,"setTimeout"),O(g,"setInterval"),Nb(g),vb(g),zc.g=g);return zc})("",void 0,"/_/AccountsDomainCookiesCheckConnectionHttp/jserror");C("google.checkconnection.getMsgToSend",function(a,b){if(!a)throw Error("s");!b||b<=0?b=0:(b=(new Date).getTime()-b,b=!b||b<0?0:b);return a+":"+String(b)});
}catch(e){_._DumpException(e)}
}).call(this,this.default_AccountsDomaincookiesCheckconnectionJs);
// Google Inc.

//# sourceURL=/_/mss/boq-identity/_/js/k=boq-identity.AccountsDomaincookiesCheckconnectionJs.en_US.kmfjjmqN7fA.2018.O/am=AMA/d=1/rs=AOaEmlEDuN-amSuX_8AzxpDbVKS_QrbQAA/m=base
if (window.parent && window.parent.postMessage) {window.parent.postMessage( google.checkconnection.getMsgToSend('youtube', '*************'), 'https:\/\/accounts.google.com');}</script></head><body></body></html>