{"update_url": "https://clients2.google.com/service/update2/crx", "name": "WebRTC Protect - Protect IP Leak", "description": "__MSG_description__", "version": "0.2.4", "manifest_version": 3, "default_locale": "en", "permissions": ["storage", "privacy", "contextMenus"], "host_permissions": ["*://*/*"], "background": {"service_worker": "worker.js"}, "action": {}, "homepage_url": "https://webextension.org/listing/webrtc-protect.html", "icons": {"16": "/data/icons/16.png", "32": "/data/icons/32.png", "48": "/data/icons/48.png", "64": "/data/icons/64.png", "128": "/data/icons/128.png", "256": "/data/icons/256.png", "512": "/data/icons/512.png"}, "options_ui": {"page": "/data/options/index.html"}, "content_scripts": [{"matches": ["*://*/*"], "js": ["/data/inject/main.js"], "run_at": "document_start", "all_frames": true, "match_about_blank": true, "world": "MAIN"}, {"matches": ["*://*/*"], "js": ["/data/inject/isolated.js"], "run_at": "document_start", "all_frames": true, "match_about_blank": true, "world": "ISOLATED"}], "commands": {"_execute_action": {}}}