body {
  font-size: 14px;
  font-family: <PERSON><PERSON>, "Helvetica Neue", Helvetica, sans-serif;
  background-color: #fff;
  color: #4d5156;
  margin: 10px;
}
select,
button,
input[type=submit],
input[type=button] {
  height: 28px;
  color: #444;
  background-image: linear-gradient(rgb(237, 237, 237), rgb(237, 237, 237) 38%, rgb(222, 222, 222));
  box-shadow: rgba(0, 0, 0, 0.08) 0 1px 0, rgba(255, 255, 255, 0.75) 0 1px 2px inset;
  text-shadow: rgb(240, 240, 240) 0 1px 0;
}
select,
button,
textarea,
input[type=submit],
input[type=button] {
  border: solid 1px rgba(0, 0, 0, 0.25);
}
select {
  width: 100%;
}
input[type=button]:disabled {
  opacity: 0.5;
}
input[type=radio],
input[type=checkbox] {
  margin: 0;
}
textarea {
  width: 100%;
  box-sizing: border-box;
  display: block;
}
textarea,
input[type=text],
input[type=number] {
  padding: 5px;
  outline: none;
}
textarea:focus,
input[type=text]:focus,
input[type=number]:focus {
  background-color: #e5f8ff;
}
a,
a:visited {
  color: #07c;
}

#incognito {
  color: #fff;
  background-color: #e41655;
  padding: 2px 10px;
  border-radius: 2px;
}
#incognito[data-enabled=true] {
  background-color: #1b8057;
}

.second {
  display: grid;
  grid-template-columns: min-content 1fr;
  grid-gap: 10px;
  align-items: center;
}
.fgtt {
  display: grid;
  grid-template-columns: 1fr min-content;
  grid-gap: 10px;
  align-items: center;
}
.note {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 5px 10px;
  margin-top: 5px;
}

label[for="device-enum-api"] {
  grid-column: 2/4;
}

#buttons {
  display: grid;
  grid-template-columns: min-content 1fr;
  grid-gap: 5px;
  align-items: center;
}
#support {
  justify-self: start;
}
