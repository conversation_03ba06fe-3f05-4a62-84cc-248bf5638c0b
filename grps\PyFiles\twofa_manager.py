#!/usr/bin/env python3
"""
Two-Factor Authentication (2FA) Manager
Handles secure storage and management of 2FA secrets for profiles
"""

import os
import json
import base64
import logging
from typing import Dict, Optional, List, Tuple
from datetime import datetime
import pyotp
import qrcode
from PIL import Image
from pyzbar import pyzbar
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC


class TwoFAManager:
    """
    Manages Two-Factor Authentication secrets with encrypted storage
    """
    
    def __init__(self, storage_dir: str = "grps/data/2fa_secrets", master_password: Optional[str] = None):
        """
        Initialize 2FA Manager
        
        Args:
            storage_dir: Directory to store encrypted 2FA secrets
            master_password: Master password for encryption (if None, will prompt)
        """
        self.storage_dir = storage_dir
        self.logger = logging.getLogger(f"TwoFAManager")
        
        # Ensure storage directory exists
        os.makedirs(self.storage_dir, exist_ok=True)
        
        # Initialize encryption
        self.master_password = master_password
        self._encryption_key = None
        self._initialize_encryption()
        
        self.logger.info(f"2FA Manager initialized with storage: {self.storage_dir}")
    
    def _initialize_encryption(self):
        """Initialize encryption key from master password"""
        if not self.master_password:
            # For now, use a default password - in production, this should be prompted
            self.master_password = "default_2fa_master_key_change_in_production"
            self.logger.warning("Using default master password - change in production!")
        
        # Derive encryption key from master password
        password_bytes = self.master_password.encode()
        salt = b'2fa_salt_change_in_production'  # In production, use random salt per file
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
        self._encryption_key = Fernet(key)
        
        self.logger.info("Encryption key initialized")
    
    def _get_profile_file_path(self, profile_identifier: str) -> str:
        """Get the file path for a profile's 2FA secrets"""
        safe_identifier = "".join(c for c in profile_identifier if c.isalnum() or c in "_-")
        return os.path.join(self.storage_dir, f"{safe_identifier}_2fa.enc")
    
    def _encrypt_data(self, data: dict) -> bytes:
        """Encrypt profile data"""
        json_data = json.dumps(data, indent=2)
        return self._encryption_key.encrypt(json_data.encode())
    
    def _decrypt_data(self, encrypted_data: bytes) -> dict:
        """Decrypt profile data"""
        decrypted_bytes = self._encryption_key.decrypt(encrypted_data)
        return json.loads(decrypted_bytes.decode())
    
    def store_2fa_secret(self, profile_identifier: str, service_name: str, secret: str, 
                        issuer: Optional[str] = None, account_name: Optional[str] = None) -> bool:
        """
        Store a 2FA secret for a profile
        
        Args:
            profile_identifier: Unique identifier for the profile
            service_name: Name of the service (e.g., 'google', 'github')
            secret: Base32 encoded secret key
            issuer: Optional issuer name
            account_name: Optional account name
            
        Returns:
            bool: True if stored successfully
        """
        try:
            # Validate secret
            if not self._validate_secret(secret):
                self.logger.error(f"Invalid 2FA secret format for {profile_identifier}:{service_name}")
                return False
            
            # Load existing data or create new
            profile_data = self._load_profile_data(profile_identifier)
            
            # Store secret data
            secret_data = {
                'secret': secret,
                'service_name': service_name,
                'issuer': issuer or service_name,
                'account_name': account_name or profile_identifier,
                'created_at': datetime.now().isoformat(),
                'last_used': None,
                'usage_count': 0
            }
            
            profile_data['secrets'][service_name] = secret_data
            profile_data['last_updated'] = datetime.now().isoformat()
            
            # Save encrypted data
            success = self._save_profile_data(profile_identifier, profile_data)
            
            if success:
                self.logger.info(f"2FA secret stored for {profile_identifier}:{service_name}")
            else:
                self.logger.error(f"Failed to store 2FA secret for {profile_identifier}:{service_name}")
                
            return success
            
        except Exception as e:
            self.logger.error(f"Error storing 2FA secret for {profile_identifier}:{service_name}: {e}")
            return False
    
    def get_2fa_code(self, profile_identifier: str, service_name: str) -> Optional[str]:
        """
        Generate current 2FA code for a profile and service
        
        Args:
            profile_identifier: Unique identifier for the profile
            service_name: Name of the service
            
        Returns:
            str: Current 6-digit 2FA code, or None if not found
        """
        try:
            profile_data = self._load_profile_data(profile_identifier)
            
            if service_name not in profile_data['secrets']:
                self.logger.warning(f"No 2FA secret found for {profile_identifier}:{service_name}")
                return None
            
            secret_data = profile_data['secrets'][service_name]
            secret = secret_data['secret']
            
            # Generate TOTP code
            totp = pyotp.TOTP(secret)
            code = totp.now()
            
            # Update usage statistics
            secret_data['last_used'] = datetime.now().isoformat()
            secret_data['usage_count'] = secret_data.get('usage_count', 0) + 1
            profile_data['last_updated'] = datetime.now().isoformat()
            
            # Save updated data
            self._save_profile_data(profile_identifier, profile_data)
            
            self.logger.info(f"Generated 2FA code for {profile_identifier}:{service_name}")
            return code
            
        except Exception as e:
            self.logger.error(f"Error generating 2FA code for {profile_identifier}:{service_name}: {e}")
            return None
    
    def _validate_secret(self, secret: str) -> bool:
        """Validate that a secret is properly formatted Base32"""
        try:
            # Remove spaces and convert to uppercase
            clean_secret = secret.replace(' ', '').upper()
            
            # Check if it's valid Base32
            base64.b32decode(clean_secret)
            
            # Test if it can create a valid TOTP
            totp = pyotp.TOTP(clean_secret)
            totp.now()  # This will raise an exception if invalid
            
            return True
        except Exception:
            return False
    
    def _load_profile_data(self, profile_identifier: str) -> dict:
        """Load profile data from encrypted file"""
        file_path = self._get_profile_file_path(profile_identifier)
        
        if not os.path.exists(file_path):
            # Return empty profile structure
            return {
                'profile_identifier': profile_identifier,
                'secrets': {},
                'created_at': datetime.now().isoformat(),
                'last_updated': datetime.now().isoformat()
            }
        
        try:
            with open(file_path, 'rb') as f:
                encrypted_data = f.read()
            
            return self._decrypt_data(encrypted_data)
            
        except Exception as e:
            self.logger.error(f"Error loading profile data for {profile_identifier}: {e}")
            # Return empty structure on error
            return {
                'profile_identifier': profile_identifier,
                'secrets': {},
                'created_at': datetime.now().isoformat(),
                'last_updated': datetime.now().isoformat()
            }
    
    def _save_profile_data(self, profile_identifier: str, data: dict) -> bool:
        """Save profile data to encrypted file"""
        try:
            file_path = self._get_profile_file_path(profile_identifier)
            encrypted_data = self._encrypt_data(data)
            
            with open(file_path, 'wb') as f:
                f.write(encrypted_data)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving profile data for {profile_identifier}: {e}")
            return False
    
    def list_profiles(self) -> List[str]:
        """List all profiles with 2FA secrets"""
        profiles = []
        
        try:
            for filename in os.listdir(self.storage_dir):
                if filename.endswith('_2fa.enc'):
                    profile_id = filename.replace('_2fa.enc', '')
                    profiles.append(profile_id)
        except Exception as e:
            self.logger.error(f"Error listing profiles: {e}")
        
        return profiles
    
    def get_profile_services(self, profile_identifier: str) -> List[str]:
        """Get list of services with 2FA secrets for a profile"""
        try:
            profile_data = self._load_profile_data(profile_identifier)
            return list(profile_data['secrets'].keys())
        except Exception as e:
            self.logger.error(f"Error getting services for {profile_identifier}: {e}")
            return []
    
    def remove_2fa_secret(self, profile_identifier: str, service_name: str) -> bool:
        """Remove a 2FA secret for a profile and service"""
        try:
            profile_data = self._load_profile_data(profile_identifier)
            
            if service_name in profile_data['secrets']:
                del profile_data['secrets'][service_name]
                profile_data['last_updated'] = datetime.now().isoformat()
                
                success = self._save_profile_data(profile_identifier, profile_data)
                
                if success:
                    self.logger.info(f"Removed 2FA secret for {profile_identifier}:{service_name}")
                
                return success
            else:
                self.logger.warning(f"No 2FA secret found to remove for {profile_identifier}:{service_name}")
                return True  # Nothing to remove is considered success
                
        except Exception as e:
            self.logger.error(f"Error removing 2FA secret for {profile_identifier}:{service_name}: {e}")
            return False

    def scan_qr_code(self, image_path: str) -> Optional[Dict[str, str]]:
        """
        Scan QR code from image file and extract 2FA information

        Args:
            image_path: Path to QR code image file

        Returns:
            dict: Extracted 2FA information or None if failed
        """
        try:
            # Load and decode QR code
            image = Image.open(image_path)
            decoded_objects = pyzbar.decode(image)

            if not decoded_objects:
                self.logger.warning(f"No QR code found in image: {image_path}")
                return None

            # Get the first QR code data
            qr_data = decoded_objects[0].data.decode('utf-8')
            self.logger.info(f"QR code data extracted: {qr_data[:50]}...")

            # Parse TOTP URL
            return self._parse_totp_url(qr_data)

        except Exception as e:
            self.logger.error(f"Error scanning QR code from {image_path}: {e}")
            return None

    def scan_qr_code_from_screenshot(self, screenshot_data: bytes) -> Optional[Dict[str, str]]:
        """
        Scan QR code from screenshot data

        Args:
            screenshot_data: Raw screenshot bytes

        Returns:
            dict: Extracted 2FA information or None if failed
        """
        try:
            # Convert bytes to PIL Image
            from io import BytesIO
            image = Image.open(BytesIO(screenshot_data))

            # Decode QR code
            decoded_objects = pyzbar.decode(image)

            if not decoded_objects:
                self.logger.warning("No QR code found in screenshot")
                return None

            # Get the first QR code data
            qr_data = decoded_objects[0].data.decode('utf-8')
            self.logger.info(f"QR code data extracted from screenshot: {qr_data[:50]}...")

            # Parse TOTP URL
            return self._parse_totp_url(qr_data)

        except Exception as e:
            self.logger.error(f"Error scanning QR code from screenshot: {e}")
            return None

    def _parse_totp_url(self, totp_url: str) -> Optional[Dict[str, str]]:
        """
        Parse TOTP URL and extract components

        Args:
            totp_url: TOTP URL (e.g., otpauth://totp/...)

        Returns:
            dict: Parsed components
        """
        try:
            from urllib.parse import urlparse, parse_qs

            if not totp_url.startswith('otpauth://totp/'):
                self.logger.error(f"Invalid TOTP URL format: {totp_url}")
                return None

            # Parse URL
            parsed = urlparse(totp_url)
            query_params = parse_qs(parsed.query)

            # Extract components
            path_parts = parsed.path.lstrip('/').split(':')

            result = {
                'secret': query_params.get('secret', [''])[0],
                'issuer': query_params.get('issuer', [''])[0],
                'account_name': path_parts[-1] if path_parts else '',
                'algorithm': query_params.get('algorithm', ['SHA1'])[0],
                'digits': int(query_params.get('digits', ['6'])[0]),
                'period': int(query_params.get('period', ['30'])[0])
            }

            # If issuer is in the path, use it
            if len(path_parts) > 1:
                result['issuer'] = path_parts[0]

            # Validate required fields
            if not result['secret']:
                self.logger.error("No secret found in TOTP URL")
                return None

            self.logger.info(f"Parsed TOTP URL - Issuer: {result['issuer']}, Account: {result['account_name']}")
            return result

        except Exception as e:
            self.logger.error(f"Error parsing TOTP URL: {e}")
            return None

    def store_from_qr_code(self, profile_identifier: str, image_path: str,
                          service_name: Optional[str] = None) -> bool:
        """
        Scan QR code and store 2FA secret for profile

        Args:
            profile_identifier: Unique identifier for the profile
            image_path: Path to QR code image
            service_name: Optional service name override

        Returns:
            bool: True if stored successfully
        """
        try:
            # Scan QR code
            qr_info = self.scan_qr_code(image_path)
            if not qr_info:
                return False

            # Use provided service name or extract from QR code
            final_service_name = service_name or qr_info['issuer'] or 'unknown_service'

            # Store the secret
            return self.store_2fa_secret(
                profile_identifier=profile_identifier,
                service_name=final_service_name,
                secret=qr_info['secret'],
                issuer=qr_info['issuer'],
                account_name=qr_info['account_name']
            )

        except Exception as e:
            self.logger.error(f"Error storing from QR code for {profile_identifier}: {e}")
            return False

    def generate_backup_codes(self, profile_identifier: str, service_name: str, count: int = 10) -> List[str]:
        """
        Generate backup codes for a 2FA service (for manual backup)

        Args:
            profile_identifier: Unique identifier for the profile
            service_name: Name of the service
            count: Number of backup codes to generate

        Returns:
            list: Generated backup codes
        """
        try:
            import secrets
            import string

            backup_codes = []
            for _ in range(count):
                # Generate 8-character alphanumeric code
                code = ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(8))
                backup_codes.append(code)

            # Store backup codes in profile data
            profile_data = self._load_profile_data(profile_identifier)

            if service_name in profile_data['secrets']:
                profile_data['secrets'][service_name]['backup_codes'] = {
                    'codes': backup_codes,
                    'generated_at': datetime.now().isoformat(),
                    'used_codes': []
                }
                profile_data['last_updated'] = datetime.now().isoformat()

                self._save_profile_data(profile_identifier, profile_data)
                self.logger.info(f"Generated {count} backup codes for {profile_identifier}:{service_name}")

            return backup_codes

        except Exception as e:
            self.logger.error(f"Error generating backup codes for {profile_identifier}:{service_name}: {e}")
            return []
